import moment from "moment";
import { doIntervalsOverlap } from "../helper/custom.helper";
import { ScheduleDao } from "../lib/dao/schedule.dao";
import { throwError } from "../util/functions";
import { Utility } from "../util/util";
import ScheduleModel, { ScheduleStatus } from "../models/Schedule.model";
import { ClientService } from "./client.service";
import { TherapistService } from "./therapist.service";
import mongoose from "mongoose";

export interface ICreateScheduleData {
  clientId: any;
  additionalEmails: any;
  duration: any;
  clientCountry: any;
  recurrence: any;
  tillDate: any;
  description: any;
  recurrenceDates: any;
  location: any;
  age: any;
  gender: any;
  paymentIsBefore: boolean;
  summary: any;
  fromPublicCalender: any;
}
export class ScheduleService {
  static async create(therapistId: any, schedule: ICreateScheduleData) {
    const client = await ClientService.getClientById(schedule.clientId);
    const therapist = await TherapistService.getTherapist(therapistId);
    if (!client) {
      return throwError("Client not found.", 400);
    }
    if (!therapist) {
      return throwError("Therapist not found.", 400);
    }

    const schedule_info = await ScheduleDao.createSchedule({
      clientId: schedule.clientId,
      therapistId: therapistId,
      scheduleId: therapist.identifier + "_" + Utility.generateRandomNumber(),
      name: client.name,
      phone: client.phone,
      email: client.email,
      additionalEmails: schedule.additionalEmails,
      durationOfSchedule: schedule.duration,
      clientCountry: schedule.clientCountry,
      recurrence: schedule.recurrence,
      tillDate: schedule.tillDate,
      description: schedule.description,
      recurrenceDates: schedule.recurrenceDates.map((recurrenceDate: any) => {
        return {
          fromDate: recurrenceDate.fromDate,
          toDate: recurrenceDate.toDate,
          status: schedule.paymentIsBefore
            ? ScheduleStatus.PENDING
            : ScheduleStatus.CONFIRMED,
          amount: recurrenceDate.amount,
          payLater: schedule.paymentIsBefore ? false : true,
        };
      }),
      location: schedule.location,
      age: schedule.age,
      gender: schedule.gender,
      summary: schedule.summary,
      fromPublicCalender: schedule.fromPublicCalender,
    });

    return schedule_info;
  }

  static async createSchedule(therapistId: any, scheduleData: any, email: any) {
    const schedules: any = await ScheduleDao.getAllRecurrenceDatesByTherapist(
      therapistId
    );
    let schedule: any;
    if (!schedules || schedules.length == 0) {
      if (scheduleData.isBefore == false) {
        for (let requestedRecurrenceDate of scheduleData.recurrenceDates) {
          requestedRecurrenceDate.status = ScheduleStatus.CONFIRMED;
        }
      }
      const scheduleId = Utility.generateRandomString();
      scheduleData.scheduleId = scheduleId;
      schedule = await ScheduleDao.createSchedule(scheduleData);
      if (!schedule) {
        return throwError("Unable to create appointment.", 400);
      }
      return schedule;
    }
    let allRecurrenceDates: any = [];
    for (let schedule of schedules) {
      // Filter out cancelled sessions
      const activeDates = schedule.recurrenceDates.filter(
        (date: any) => date.status !== ScheduleStatus.CANCELLED
      );
      allRecurrenceDates.push(activeDates);
    }
    const reqRecurrenceDates: any = [].concat(...allRecurrenceDates);
    let hasOverlap = false;
    const existingRecurrenceDates = reqRecurrenceDates;
    for (let existingRecurrenceDate of existingRecurrenceDates) {
      for (let requestedRecurrenceDate of scheduleData.recurrenceDates) {
        const intervalsOverlap = doIntervalsOverlap(
          existingRecurrenceDate,
          requestedRecurrenceDate
        );
        if (intervalsOverlap) {
          hasOverlap = true;
          break;
        }
      }
      if (hasOverlap) {
        break;
      }
    }
    if (hasOverlap) {
      return throwError("Time Interval overlap.", 400);
    }
    const existingEmailForMultiple: any =
      await ScheduleDao.getByEmailForMultiple(therapistId, email);
    const existingEmailForSingle: any = await ScheduleDao.getByEmailForSingle(
      therapistId,
      email
    );
    if (existingEmailForSingle) {
      if (scheduleData.isBefore == false) {
        for (let requestedRecurrenceDate of scheduleData.recurrenceDates) {
          requestedRecurrenceDate.status = ScheduleStatus.CONFIRMED;
        }
      }
      const updateSchedule = await ScheduleDao.updateByEmail(
        existingEmailForSingle._id,
        therapistId,
        email,
        scheduleData.recurrenceDates
      );
      if (!updateSchedule) {
        return throwError("Unable to update schedule.", 400);
      }

      return updateSchedule;
    }
    if (existingEmailForMultiple) {
      if (scheduleData.isBefore == false) {
        for (let requestedRecurrenceDate of scheduleData.recurrenceDates) {
          requestedRecurrenceDate.status = ScheduleStatus.CONFIRMED;
        }
      }
      const scheduleId = Utility.generateRandomString();
      scheduleData.scheduleId = scheduleId;
      scheduleData.email = email;
      schedule = await ScheduleDao.createSchedule(scheduleData);
      if (!schedule) {
        return throwError("Unable to create appointment.", 400);
      }
      return schedule;
    }
    if (!existingEmailForMultiple && !existingEmailForSingle) {
      if (scheduleData.isBefore == false) {
        for (let requestedRecurrenceDate of scheduleData.recurrenceDates) {
          requestedRecurrenceDate.status = ScheduleStatus.CONFIRMED;
        }
      }
      const scheduleId = Utility.generateRandomString();
      scheduleData.scheduleId = scheduleId;
      scheduleData.email = email;
      schedule = await ScheduleDao.createSchedule(scheduleData);
      if (!schedule) {
        return throwError("Unable to create appointment.", 400);
      }
      return schedule;
    }
  }

  static async createScheduleForTwo(therapistId: any, scheduleData: any) {
    const schedules: any = await ScheduleDao.getAllRecurrenceDatesByTherapist(
      therapistId
    );
    let schedule: any;
    if (!schedules || schedules.length == 0) {
      if (scheduleData.isBefore == false) {
        for (let requestedRecurrenceDate of scheduleData.recurrenceDates) {
          requestedRecurrenceDate.status = ScheduleStatus.CONFIRMED;
        }
      }
      const scheduleId = Utility.generateRandomString();
      scheduleData.scheduleId = scheduleId;
      scheduleData.email = scheduleData.emails[0];
      scheduleData.additionalEmails = scheduleData.emails.slice(1);
      scheduleData.scheduleForMultiple = true;
      schedule = await ScheduleDao.createSchedule(scheduleData);
      if (!schedule) {
        return throwError("Unable to create appointment.", 400);
      }
      return schedule;
    }
    let allRecurrenceDates: any = [];
    for (let schedule of schedules) {
      // Filter out cancelled sessions
      const activeDates = schedule.recurrenceDates.filter(
        (date: any) => date.status !== ScheduleStatus.CANCELLED
      );
      allRecurrenceDates.push(activeDates);
    }
    const reqRecurrenceDates: any = [].concat(...allRecurrenceDates);
    let hasOverlap = false;
    const existingRecurrenceDates = reqRecurrenceDates;
    for (let existingRecurrenceDate of existingRecurrenceDates) {
      for (let requestedRecurrenceDate of scheduleData.recurrenceDates) {
        const intervalsOverlap = doIntervalsOverlap(
          existingRecurrenceDate,
          requestedRecurrenceDate
        );
        if (intervalsOverlap) {
          hasOverlap = true;
          break;
        }
      }
      if (hasOverlap) {
        break;
      }
    }
    if (hasOverlap) {
      return throwError("Time Interval overlap.", 400);
    }
    const existingEmails = await ScheduleDao.getByEmails(
      therapistId,
      scheduleData.emails
    );
    if (existingEmails) {
      if (scheduleData.isBefore == false) {
        for (let requestedRecurrenceDate of scheduleData.recurrenceDates) {
          requestedRecurrenceDate.status = ScheduleStatus.CONFIRMED;
        }
      }
      const updateSchedule = await ScheduleDao.updateForEmails(
        existingEmails._id,
        therapistId,
        scheduleData.recurrenceDates
      );
      if (!updateSchedule) {
        return throwError("Unable to update schedule.", 400);
      }
    } else {
      if (scheduleData.isBefore == false) {
        for (let requestedRecurrenceDate of scheduleData.recurrenceDates) {
          requestedRecurrenceDate.status = ScheduleStatus.CONFIRMED;
        }
      }
      const scheduleId = Utility.generateRandomString();
      scheduleData.scheduleId = scheduleId;
      scheduleData.email = scheduleData.emails[0];
      scheduleData.additionalEmails = scheduleData.emails.slice(1);
      scheduleData.scheduleForMultiple = true;
      const schedule = await ScheduleDao.createSchedule(scheduleData);
      if (!schedule) {
        return throwError("Unable to create appointment.", 400);
      }
    }
    return schedule;
  }

  static async updateRecurrenceDate(
    scheduleId: any,
    recurrenceDateId: any,
    transactionId: any,
    meetLink: string,
    calenderEventId: any,
    payTrackerId: any
  ) {
    return await ScheduleDao.updateRecurrenceDate(
      scheduleId,
      recurrenceDateId,
      transactionId,
      meetLink,
      calenderEventId,
      payTrackerId
    );
  }

  static async getSchedules(
    sessions: any,
    skip: any,
    pageSize: any,
    name: any,
    startDate: any,
    endDate: any
  ) {
    let paginationData;
    let count;
    if (name && !startDate && !endDate) {
      const nameFilteredData = await sessions.sort((a: any, b: any) =>
        a["name"].localeCompare(b["name"])
      );
      count = nameFilteredData.length;
      paginationData = await nameFilteredData.slice(skip, skip + pageSize);
    }
    if (!name && startDate && endDate) {
      const dateFiltered = await sessions.filter(
        (schedule: any) =>
          moment(schedule.toDate) >= moment(startDate) &&
          moment(schedule.toDate) <= moment(endDate)
      );
      count = dateFiltered.length;
      const dateFilteredData = await dateFiltered.sort(
        (a: any, b: any) => a["toDate"] - b["toDate"]
      );
      paginationData = await dateFilteredData.slice(skip, skip + pageSize);
    }
    if (name && startDate && endDate) {
      const dateFiltered = await sessions.filter(
        (schedule: any) =>
          moment(schedule.toDate) >= moment(startDate) &&
          moment(schedule.toDate) <= moment(endDate)
      );
      count = dateFiltered.length;
      const nameFiltered = await dateFiltered.sort((a: any, b: any) =>
        a["name"].localeCompare(b["name"])
      );
      paginationData = await nameFiltered.slice(skip, skip + pageSize);
    }
    if (startDate == undefined && endDate == undefined && name == undefined) {
      count = sessions.length;
      const dateFilteredData = await sessions.sort(
        (a: any, b: any) => a["toDate"] - b["toDate"]
      );
      paginationData = await dateFilteredData.slice(skip, skip + pageSize);
    }
    return {
      paginationData,
      count,
    };
  }

  static async getScheduleByTherapistAndClientId(
    therapistId: any,
    clientId: any
  ) {
    return await ScheduleDao.getScheduleByTherapistAndClientId(
      therapistId,
      clientId
    );
  }

  static async getScheduleByScheduleIdTherapistAndClientId(
    scheduleId: any,
    therapistId: any,
    clientId: any
  ) {
    return await ScheduleDao.getScheduleByScheduleIdTherapistAndClientId(
      scheduleId,
      therapistId,
      clientId
    );
  }

  static async getScheduleByTherapistId(therapistId: any) {
    return await ScheduleDao.getScheduleByTherapistId(therapistId);
  }

  static async getScheduleListByFilter(
    filter: any,
    projection: any = {},
    options: any = {}
  ) {
    return await ScheduleDao.getScheduleByFilter(filter, projection, options);
  }

  static async getCount(days: any) {
    return await ScheduleDao.getCount(days);
  }

  static async getAll(
    pageSize: any,
    skip: any,
    therapistId: any,
    clientId: any
  ) {
    return await ScheduleDao.getAll(pageSize, skip, therapistId, clientId);
  }
  static async getSchedulesRecurrenceId(therapist: any, recurrenceId: any) {
    return await ScheduleDao.getSchedulesRecurrenceId(therapist, recurrenceId);
  }

  static async deleteRecurrance(recc_id: any) {
    return await ScheduleDao.deleteRecurrance(recc_id);
  }

  static async getAllScheduleByTherapist(therapistId: any) {
    const currentDate = new Date();
    // return await ScheduleModel.find({ therapistId });
    const schedule = await ScheduleModel.aggregate([
      {
        $match: {
          therapistId: new mongoose.Types.ObjectId(therapistId),
        },
      },
      {
        $unwind: "$recurrenceDates",
      },
      {
        $match: {
          "recurrenceDates.fromDate": { $gte: new Date(currentDate) },
        },
      },
      {
        $group: {
          _id: "$_id",
          email: { $first: "$email" },
          clientId: { $first: "$clientId" },
          therapistId: { $first: "$therapistId" },
          scheduleId: { $first: "$scheduleId" },
          summary: { $first: "$summary" },
          location: { $first: "$location" },
          description: { $first: "$description" },
          recurrenceDates: { $push: "$recurrenceDates" },
        },
      },
    ]);
    return schedule;
  }
}
